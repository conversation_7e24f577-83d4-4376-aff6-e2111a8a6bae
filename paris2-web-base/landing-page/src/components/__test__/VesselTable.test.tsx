import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VesselTable from '../dashboard/VesselWidget/VesselTable';
import { Vessel } from '../../../types/types';

// Mock the hooks and components
jest.mock('../../../../hooks/useInfiniteScroll', () => ({
  useInfiniteScroll: jest.fn(),
}));

jest.mock('../Spinner', () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

describe('VesselTable', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: [{ status: 'active', flag: 'US' }],
      type: 'cargo',
      vessel_ownership_id: 101,
      risk_id: 1,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: [{ status: 'inactive', flag: 'UK' }],
      type: 'tanker',
      vessel_ownership_id: 102,
      risk_id: 2,
      vessel_id: 2,
    },
  ];

  const defaultProps = {
    vessels: mockVessels,
    tableHeaders: ['Name', 'Status', 'Type'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    onSendEmail: jest.fn(),
    onVesselClick: jest.fn(),
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
    pagination: {
      totalItems: 10,
      totalPages: 2,
      page: 1,
      pageSize: 5,
    },
    cellStyleType: 'default' as const,
    sortConfig: null,
    onSort: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Set default mock implementation for useInfiniteScroll
    const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;
    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: jest.fn(),
    });
  });

  it('should render loading state when isLoading is true', () => {
    render(<VesselTable {...defaultProps} isLoading={true} />);
    
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render no results message when vessels array is empty', () => {
    render(<VesselTable {...defaultProps} vessels={[]} isLoading={false} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should render table headers correctly', () => {
    render(<VesselTable {...defaultProps} />);
    
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should render vessel rows correctly', () => {
    render(<VesselTable {...defaultProps} />);
    
    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Vessel Beta')).toBeInTheDocument();
  });

  it('should call onVesselClick when vessel name is clicked', () => {
    const onVesselClick = jest.fn();
    render(<VesselTable {...defaultProps} onVesselClick={onVesselClick} />);
    
    const vesselButton = screen.getByText('Vessel Alpha');
    fireEvent.click(vesselButton);
    
    expect(onVesselClick).toHaveBeenCalledWith(mockVessels[0]);
  });

  it('should call onSort when header is clicked', () => {
    const onSort = jest.fn();
    render(<VesselTable {...defaultProps} onSort={onSort} />);
    
    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);
    
    expect(onSort).toHaveBeenCalledWith('Name');
  });

  it('should show loading indicator when fetching next page', () => {
    render(<VesselTable {...defaultProps} isFetchingNextPage={true} />);
    
    const loadingIndicators = screen.getAllByTestId('spinner');
    expect(loadingIndicators).toHaveLength(1); // One for the loading indicator
  });

  it('should handle pagination correctly', () => {
    const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;
    const mockFetchNextPage = jest.fn();

    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: jest.fn(),
    });

    render(
      <VesselTable
        {...defaultProps}
        fetchNextPage={mockFetchNextPage}
        pagination={{
          totalItems: 20,
          totalPages: 4,
          page: 2,
          pageSize: 5,
        }}
      />
    );

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: mockFetchNextPage,
      isFetchingNextPage: false,
      hasNextPage: true, // page 2 < totalPages 4
      dataLength: 2, // vessels.length
    });
  });

  it('should handle no pagination', () => {
    const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;

    render(<VesselTable {...defaultProps} pagination={undefined} />);

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // no pagination means no next page
      dataLength: 2,
    });
  });

  it('should handle conditional cell styling', () => {
    render(<VesselTable {...defaultProps} cellStyleType="conditional" />);
    
    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Vessel Beta')).toBeInTheDocument();
  });

  it('should handle undefined vessels array', () => {
    render(<VesselTable {...defaultProps} vessels={undefined as any} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('should handle empty tableHeaders', () => {
    render(<VesselTable {...defaultProps} tableHeaders={[]} />);
    
    // Should still render Actions header
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should filter out action headers correctly', () => {
    render(<VesselTable {...defaultProps} tableHeaders={['Name', 'Action', 'Status']} />);
    
    // Should render Name and Status but not duplicate Action
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should handle scroll events', () => {
    const mockHandleScroll = jest.fn();
    const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;

    useInfiniteScroll.mockReturnValue({
      containerRef: { current: null },
      handleScroll: mockHandleScroll,
    });

    const { container } = render(<VesselTable {...defaultProps} />);

    // Find the div that contains the table (should be the first div)
    const tableContainer = container.querySelector('div');
    expect(tableContainer).toBeInTheDocument();

    // Verify that useInfiniteScroll was called (which means scroll handling is set up)
    expect(useInfiniteScroll).toHaveBeenCalled();
  });

  it('should handle vessels with missing properties', () => {
    const incompleteVessels: Vessel[] = [
      {
        name: 'Incomplete Vessel',
        vesselData: [],
        type: 'unknown',
      } as Vessel,
    ];
    
    render(<VesselTable {...defaultProps} vessels={incompleteVessels} />);
    
    expect(screen.getByText('Incomplete Vessel')).toBeInTheDocument();
  });

  it('should handle sort configuration', () => {
    const sortConfig = {
      key: 'Name',
      direction: 'ascending' as const,
    };
    
    render(<VesselTable {...defaultProps} sortConfig={sortConfig} />);
    
    expect(screen.getByText('Name')).toBeInTheDocument();
  });

  it('should handle last page correctly', () => {
    const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;

    render(
      <VesselTable
        {...defaultProps}
        pagination={{
          totalItems: 10,
          totalPages: 2,
          page: 2, // Last page
          pageSize: 5,
        }}
      />
    );

    expect(useInfiniteScroll).toHaveBeenCalledWith({
      fetchNextPage: expect.any(Function),
      isFetchingNextPage: false,
      hasNextPage: false, // page 2 === totalPages 2
      dataLength: 2,
    });
  });

  it('should render table structure correctly', () => {
    const { container } = render(<VesselTable {...defaultProps} />);
    
    const table = container.querySelector('table');
    expect(table).toBeInTheDocument();
    
    const thead = container.querySelector('thead');
    expect(thead).toBeInTheDocument();
    
    const tbody = container.querySelector('tbody');
    expect(tbody).toBeInTheDocument();
  });

  it('should handle multiple vessel clicks', () => {
    const onVesselClick = jest.fn();
    render(<VesselTable {...defaultProps} onVesselClick={onVesselClick} />);
    
    const vesselAlpha = screen.getByText('Vessel Alpha');
    const vesselBeta = screen.getByText('Vessel Beta');
    
    fireEvent.click(vesselAlpha);
    fireEvent.click(vesselBeta);
    
    expect(onVesselClick).toHaveBeenCalledTimes(2);
    expect(onVesselClick).toHaveBeenNthCalledWith(1, mockVessels[0]);
    expect(onVesselClick).toHaveBeenNthCalledWith(2, mockVessels[1]);
  });

  it('should handle complex pagination scenarios', () => {
    const scenarios = [
      { page: 1, totalPages: 1, expectedHasNext: false },
      { page: 1, totalPages: 5, expectedHasNext: true },
      { page: 3, totalPages: 3, expectedHasNext: false },
      { page: 2, totalPages: 10, expectedHasNext: true },
    ];

    scenarios.forEach(({ page, totalPages, expectedHasNext }) => {
      const useInfiniteScroll = require('../../../../hooks/useInfiniteScroll').useInfiniteScroll;

      const { unmount } = render(
        <VesselTable
          {...defaultProps}
          pagination={{
            totalItems: totalPages * 5,
            totalPages,
            page,
            pageSize: 5,
          }}
        />
      );

      expect(useInfiniteScroll).toHaveBeenCalledWith({
        fetchNextPage: expect.any(Function),
        isFetchingNextPage: false,
        hasNextPage: expectedHasNext,
        dataLength: 2,
      });

      unmount();
    });
  });
});
