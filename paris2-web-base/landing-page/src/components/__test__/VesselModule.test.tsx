import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import VesselModule from '../dashboard/VesselWidget/VesselModule';
import { Vessel, MultiVesselSelectConfig } from '../../types/types';

// Mock the child components
jest.mock('../dashboard/VesselWidget/VesselTable', () => {
  return function MockVesselTable({ vessels, onSort, sortConfig, onSendEmail, onVesselClick }: any) {
    return (
      <div data-testid="vessel-table">
        <div>Vessels: {vessels?.length || 0}</div>
        <div>Sort Key: {sortConfig?.key || 'none'}</div>
        <div>Sort Direction: {sortConfig?.direction || 'none'}</div>
        <button onClick={() => onSort('Name')}>Sort by Name</button>
        <button onClick={() => onSendEmail(vessels[0])}>Send Email</button>
        <button onClick={() => onVesselClick(vessels[0])}>Click Vessel</button>
      </div>
    );
  };
});

jest.mock('../dashboard/VesselWidget/VesselGrid', () => {
  return function MockVesselGrid({ vessels, isModal }: any) {
    return (
      <div data-testid="vessel-grid">
        <div>Vessels: {vessels?.length || 0}</div>
        <div>Is Modal: {isModal.toString()}</div>
      </div>
    );
  };
});

jest.mock('../dashboard/VesselWidget/VesselModuleHeader', () => ({
  VesselModuleHeader: ({ title, viewMode, isModal, onViewModeChange, onToggleModal }: any) => (
    <div data-testid="vessel-module-header">
      <div>Title: {title}</div>
      <div>View Mode: {viewMode}</div>
      <div>Is Modal: {isModal.toString()}</div>
      <button onClick={() => onViewModeChange('grid')}>Switch to Grid</button>
      <button onClick={() => onViewModeChange('list')}>Switch to List</button>
      <button onClick={onToggleModal}>Toggle Modal</button>
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/VesselSelectors', () => ({
  VesselSelectors: ({ multiVesselSelects, selectStates, onSelectChange }: any) => (
    <div data-testid="vessel-selectors">
      <div>Selects: {multiVesselSelects?.length || 0}</div>
      <div>States: {selectStates?.length || 0}</div>
      <button onClick={() => onSelectChange(0, ['test'])}>Change Selection</button>
    </div>
  ),
}));

jest.mock('../dashboard/VesselWidget/ModuleModal', () => ({
  ModuleModal: ({ isOpen, onClose, children }: any) => (
    isOpen ? (
      <div data-testid="module-modal">
        <button onClick={onClose}>Close Modal</button>
        {children}
      </div>
    ) : null
  ),
}));

jest.mock('lucide-react', () => ({
  RotateCw: ({ onClick, className }: any) => (
    <div data-testid="rotate-icon" className={className} onClick={onClick}>
      Refresh
    </div>
  ),
}));

describe('VesselModule', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: [{ status: 'active' }],
      type: 'cargo',
      vessel_ownership_id: 101,
      risk_id: 1,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: [{ status: 'inactive' }],
      type: 'tanker',
      vessel_ownership_id: 102,
      risk_id: 2,
      vessel_id: 2,
    },
  ];

  const mockMultiVesselSelects: MultiVesselSelectConfig[] = [
    {
      placeholder: 'All Vessels',
      width: '300px',
      groups: [
        {
          id: 1,
          title: 'Group 1',
          vessels: [
            { vessel_id: 1, name: 'Vessel A', vessel_ownership_id: 101 },
          ],
        },
      ],
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
  ];

  const defaultProps = {
    title: 'Test Vessel Module',
    vessels: mockVessels,
    tabs: [],
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    IsVesselSelectVisible: true,
    IsAllTabVisible: true,
    multiVesselSelects: mockMultiVesselSelects,
    vesselSelectPosition: 'before' as const,
    containerSize: { width: '100%', height: '500px' },
    modalSize: { width: '80%', height: '80%' },
    tableHeaders: ['Name', 'Status', 'Type'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    onRefresh: jest.fn(),
    gridComponent: 'bar' as const,
    defaultComponent: 'list' as const,
    cellStyleType: 'default' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with default props', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
    expect(screen.getByText('Title: Test Vessel Module')).toBeInTheDocument();
    expect(screen.getByText('View Mode: list')).toBeInTheDocument();
  });

  it('should render vessel table in list mode', () => {
    render(<VesselModule {...defaultProps} defaultComponent="list" />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-grid')).not.toBeInTheDocument();
  });

  it('should render vessel grid in grid mode', () => {
    render(<VesselModule {...defaultProps} defaultComponent="grid" />);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-table')).not.toBeInTheDocument();
  });

  it('should switch view modes correctly', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Initially in list mode
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    
    // Switch to grid mode
    fireEvent.click(screen.getByText('Switch to Grid'));
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-table')).not.toBeInTheDocument();
    
    // Switch back to list mode
    fireEvent.click(screen.getByText('Switch to List'));
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-grid')).not.toBeInTheDocument();
  });

  it('should toggle modal correctly', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Modal should not be open initially
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
    
    // Open modal
    fireEvent.click(screen.getByText('Toggle Modal'));
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
    
    // Close modal
    fireEvent.click(screen.getByText('Close Modal'));
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
  });

  it('should handle refresh functionality', async () => {
    const onRefresh = jest.fn();
    render(<VesselModule {...defaultProps} onRefresh={onRefresh} />);
    
    const refreshIcon = screen.getByTestId('rotate-icon');
    fireEvent.click(refreshIcon);
    
    expect(onRefresh).toHaveBeenCalledTimes(1);
  });

  it('should render vessel selectors when visible', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={true} />);
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should not render vessel selectors when not visible', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={false} />);
    
    expect(screen.queryByTestId('vessel-selectors')).not.toBeInTheDocument();
  });

  it('should handle vessel selector position before', () => {
    render(
      <VesselModule 
        {...defaultProps} 
        IsVesselSelectVisible={true}
        vesselSelectPosition="before"
      />
    );
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should handle vessel selector position after', () => {
    render(
      <VesselModule 
        {...defaultProps} 
        IsVesselSelectVisible={true}
        vesselSelectPosition="after"
      />
    );
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should handle sorting functionality', () => {
    render(<VesselModule {...defaultProps} />);
    
    const sortButton = screen.getByText('Sort by Name');
    fireEvent.click(sortButton);
    
    // Check that sort state is updated
    expect(screen.getByText('Sort Key: Name')).toBeInTheDocument();
    expect(screen.getByText('Sort Direction: ascending')).toBeInTheDocument();
  });

  it('should handle vessel selection changes', () => {
    render(<VesselModule {...defaultProps} />);
    
    const changeSelectionButton = screen.getByText('Change Selection');
    fireEvent.click(changeSelectionButton);
    
    // Should not crash and should handle the selection change
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('should display last updated time', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByText(/Last Updated:/)).toBeInTheDocument();
  });

  it('should handle empty vessels array', () => {
    render(<VesselModule {...defaultProps} vessels={[]} />);
    
    expect(screen.getByText('Vessels: 0')).toBeInTheDocument();
  });

  it('should handle undefined vessels', () => {
    render(<VesselModule {...defaultProps} vessels={undefined} />);
    
    expect(screen.getByText('Vessels: 0')).toBeInTheDocument();
  });

  it('should conditionally show icons based on vessel data', () => {
    // With vessels data
    const { unmount } = render(<VesselModule {...defaultProps} vessels={mockVessels} />);
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
    unmount();

    // Without vessels data
    render(<VesselModule {...defaultProps} vessels={[]} />);
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
  });

  it('should handle modal content rendering', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Open modal
    fireEvent.click(screen.getByText('Toggle Modal'));
    
    const modal = screen.getByTestId('module-modal');
    expect(modal).toBeInTheDocument();
    
    // Modal should contain the same content as main view
    expect(modal).toHaveTextContent('Title: Test Vessel Module');
  });

  it('should handle different grid components', () => {
    render(<VesselModule {...defaultProps} gridComponent="pie" defaultComponent="grid" />);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
  });

  it('should handle cell style types', () => {
    render(<VesselModule {...defaultProps} cellStyleType="conditional" />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });

  it('should handle container styling', () => {
    const { container } = render(<VesselModule {...defaultProps} />);
    
    const moduleContainer = container.querySelector('.ra-vessel-module-container');
    expect(moduleContainer).toBeInTheDocument();
  });

  it('should handle tabs prop', () => {
    const tabs = ['Tab 1', 'Tab 2'];
    render(<VesselModule {...defaultProps} tabs={tabs} />);
    
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
  });

  it('should handle badge colors', () => {
    const badgeColors = ['#red', '#green', '#blue'];
    render(<VesselModule {...defaultProps} badgeColors={badgeColors} />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });
});
